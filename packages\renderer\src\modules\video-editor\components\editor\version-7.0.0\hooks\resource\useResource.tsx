import { useCallback } from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { cacheManager } from '@/libs/cache/cache-manager.ts'
import { PasterResource } from '@/types/resources.ts'
import { useResourceLoadingStore } from './useResourceLoadingStore'

// 用于跟踪正在加载的资源，这个仍然保留为全局变量，因为它不需要触发重新渲染
const loadingResources: Record<string, Record<string, boolean>> = {}

// 初始化各类资源的加载状态对象
Object.values(ResourceType).forEach(type => {
  loadingResources[type] = {}
})

// 贴纸三层加载状态管理
const stickerLoadingStates: Record<string | number, PasterResource.StickerLoadingState> = {}

// 预加载的图片缓存
const preloadedImages: Record<string, HTMLImageElement> = {}

type DownloadResourceToCacheProps = {
  url: string;
  resourceType: ResourceType;
  version?: string;
  customExt?: string;
  id?: string | number;
}

export const useResource = () => {
  /**
   * 检查资源是否已缓存
   */
  const isResourceCached = useCallback(async (type: ResourceType, url: string): Promise<boolean> => {
    if (!type || !url) {
      console.warn('[资源缓存] 参数无效:', { type, url })
      return false
    }

    // 确保参数是可序列化的
    if (typeof type !== 'string' || typeof url !== 'string') {
      console.error('[资源缓存] 参数类型错误:', {
        type: typeof type,
        url: typeof url,
        type_value: type,
        url_value: url
      })
      return false
    }

    // 验证 ResourceType 枚举值
    if (!Object.values(ResourceType).includes(type)) {
      console.error('[资源缓存] 无效的资源类型:', type)
      return false
    }

    // 验证 URL 格式
    try {
      new URL(url)
    } catch (urlError) {
      // 如果不是完整URL，检查是否是相对路径
      if (!url.startsWith('/') && !url.startsWith('./') && !url.startsWith('../')) {
        console.error(`[资源缓存] 无效的URL格式，${type}:`, url)
        return false
      }
    }

    // 首先检查 IndexedDB 缓存
    const cache = await cacheManager.resource.getResourceCache(type, url)

    return cache !== null
  }, [])

  /**
   * 下载资源到本地缓存
   */
  const downloadResourceToCache = async (data: DownloadResourceToCacheProps): Promise<string | null> => {
    // 参数验证
    if (!data || !data.url || !data.resourceType) {
      console.error('[资源下载] 参数无效:', data)
      return null
    }

    // 验证 ResourceType 枚举值
    if (!Object.values(ResourceType).includes(data.resourceType)) {
      console.error('[资源下载] 无效的资源类型:', data.resourceType)
      return null
    }

    try {
      return cacheManager
        .resource
        .cacheResource(data.resourceType, data.url, data.version, data.customExt)
    }
    catch (error) {
      console.error('获取资源路径失败:', error)
      return null
    }
  }

  /**
   * 同步获取资源的本地缓存路径（如果已缓存）
   */
  const getResourcePathSync = useCallback((type: ResourceType, url: string): string | null => {
    return cacheManager.resource.getResourcePathSync(type, url)
  }, [])

  /**
   * 清除资源缓存
   */
  const clearResourceCache = useCallback(async (type?: ResourceType, url?: string) => {
    await cacheManager.resource.clearResourceCache(type, url)
  }, [])

  /**
   * 获取贴纸的加载状态
   */
  const getStickerLoadingState = useCallback(async (stickerId: string | number): Promise<PasterResource.StickerLoadingState> => {
    const state = await cacheManager.stickerLoading.getStickerLoadingState(stickerId)

    // 转换为 PasterResource.StickerLoadingState 格式
    return {
      coverId: state.coverId,
      coverLoaded: state.coverLoaded,
      thumbLoaded: state.thumbLoaded,
      thumbLoading: state.thumbLoading,
      fileLoaded: state.fileLoaded,
      fileLoading: state.fileLoading,
      currentLayer: state.currentLayer as PasterResource.LoadingLayer
    }
  }, [])

  /**
   * 更新贴纸加载状态
   */
  const updateStickerLoadingState = useCallback(async (
    stickerId: string | number,
    updates: Partial<PasterResource.StickerLoadingState>
  ) => {
    // 转换为 IndexedDB 格式并更新
    await cacheManager.stickerLoading.updateStickerLoadingState(stickerId, {
      ...updates,
      currentLayer: updates.currentLayer as 'cover' | 'thumb' | 'file'
    })

    // 同时更新内存状态（保留向后兼容）
    const currentState = await getStickerLoadingState(stickerId)
    stickerLoadingStates[stickerId] = { ...currentState, ...updates }
  }, [getStickerLoadingState])

  /**
   * 预加载图片（用于悬停预览）
   */
  const preloadImage = useCallback(async (url: string): Promise<HTMLImageElement> => {
    // 如果已经预加载过，直接返回
    if (preloadedImages[url]) {
      return preloadedImages[url]
    }

    // 检查 IndexedDB 中是否已缓存
    const isPreloaded = await cacheManager.image.isImagePreloaded(url)
    if (isPreloaded) {
      const cachedData = await cacheManager.image.getPreloadedImage(url)
      if (cachedData) {
        const img = new Image()
        img.src = cachedData
        preloadedImages[url] = img
        return img
      }
    }

    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = async () => {
        preloadedImages[url] = img

        // 将图片数据缓存到 IndexedDB
        try {
          await cacheManager.image.setPreloadedImage(url, img.src)
        } catch (error) {
          console.warn('缓存预加载图片失败:', error)
        }

        resolve(img)
      }
      img.onerror = reject
      img.src = url
    })
  }, [])

  /**
   * 贴纸悬停预览加载
   */
  const loadStickerPreview = useCallback(async (
    sticker: PasterResource.Paster | PasterResource.PasterLocal
  ): Promise<void> => {
    const stickerId = 'fileId' in sticker ? sticker.fileId : sticker.id
    const thumbUrl = sticker.content.thumbUrl

    // 获取当前状态
    const currentState = await getStickerLoadingState(stickerId)

    // 如果已经加载过或正在加载，直接返回
    if (currentState.thumbLoaded || currentState.thumbLoading) {
      return
    }

    // 设置加载状态
    await updateStickerLoadingState(stickerId, {
      thumbLoading: true,
      currentLayer: PasterResource.LoadingLayer.THUMB
    })

    try {
      // 预加载缩略图
      await preloadImage(thumbUrl)

      // 更新状态为已加载
      await updateStickerLoadingState(stickerId, {
        thumbLoaded: true,
        thumbLoading: false
      })

      console.log(`贴纸预览加载完成: ${thumbUrl}`)
    } catch (error) {
      console.error('贴纸预览加载失败:', error)

      // 重置加载状态
      await updateStickerLoadingState(stickerId, {
        thumbLoading: false,
        currentLayer: PasterResource.LoadingLayer.COVER
      })
    }
  }, [getStickerLoadingState, updateStickerLoadingState, preloadImage])

  /**
   * 贴纸完整资源缓存（用于编辑器）
   */
  const cacheStickerForEditor = useCallback(async (
    sticker: PasterResource.Paster | PasterResource.PasterLocal
  ): Promise<string | null> => {
    const stickerId = 'fileId' in sticker ? sticker.fileId : sticker.id
    const fileUrl = sticker.content.fileUrl

    // 获取当前状态
    const currentState = await getStickerLoadingState(stickerId)

    // 如果已经缓存过，直接返回路径
    if (currentState.fileLoaded) {
      return getResourcePathSync(ResourceType.STICKER, fileUrl)
    }

    // 如果正在加载，等待完成
    if (currentState.fileLoading) {
      // 这里可以实现等待逻辑，或者直接返回null
      return null
    }

    // 设置加载状态
    await updateStickerLoadingState(stickerId, {
      fileLoading: true,
      currentLayer: PasterResource.LoadingLayer.FILE
    })

    try {
      // 下载并缓存完整资源
      const localPath = await downloadResourceToCache({
        url: fileUrl,
        resourceType: ResourceType.STICKER,
        id: stickerId
      })

      // 更新状态为已缓存
      await updateStickerLoadingState(stickerId, {
        fileLoaded: true,
        fileLoading: false
      })

      console.log(`贴纸完整资源缓存完成: ${fileUrl} -> ${localPath}`)
      return localPath
    } catch (error) {
      console.error('贴纸完整资源缓存失败:', error)

      // 重置加载状态
      await updateStickerLoadingState(stickerId, {
        fileLoading: false
      })

      return null
    }
  }, [getStickerLoadingState, updateStickerLoadingState, downloadResourceToCache, getResourcePathSync])

  return {
    downloadResourceToCache,
    getResourcePathSync,
    isResourceCached,
    clearResourceCache,
    // 三层加载策略相关方法
    getStickerLoadingState,
    updateStickerLoadingState,
    loadStickerPreview,
    cacheStickerForEditor,
    preloadImage
  }
}
