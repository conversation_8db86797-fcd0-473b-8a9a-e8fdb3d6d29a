import { useQuery, UseQueryOptions } from '@tanstack/react-query'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { useResource } from './useResource.tsx'
import { QUERY_KEYS } from '@/constants/queryKeys.ts'

export interface ResourceCacheStatusResult {
  isCached: boolean
  isChecking: boolean
  refreshCacheStatus: () => void
}

export function useResourceCacheStatus(
  resourceType?: ResourceType,
  resourceUrl?: string,
  options: Omit<UseQueryOptions<boolean, Error>, 'queryKey' | 'queryFn'> & {
 
    refetchInterval?: number
  } = {}
): ResourceCacheStatusResult {
  const { isResourceCached } = useResource()

  const query = useQuery({
    queryKey: [QUERY_KEYS.RESOURCE_CACHE_STATUS, resourceType, resourceUrl],
    queryFn: async () => {
      if (!resourceType || !resourceUrl) {
        return false
      }
      
      return await isResourceCached(resourceType, resourceUrl)
    },
    enabled: !!(resourceType && resourceUrl),
    ...options
  })

  return {
    isCached: query.data ?? false,
    isChecking: query.isLoading || query.isFetching,
    refreshCacheStatus: query.refetch
  }
}

