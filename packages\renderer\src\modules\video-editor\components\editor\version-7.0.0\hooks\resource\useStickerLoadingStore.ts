import { create } from 'zustand'

export type LoadingLayer = 'cover' | 'thumb' | 'file'

export interface StickerLoadingState {
  stickerId: string | number
  coverLoaded: boolean
  thumbLoaded: boolean
  thumbLoading: boolean
  fileLoaded: boolean
  fileLoading: boolean
  currentLayer: LoadingLayer
  lastUpdated: number
}

export interface StickerLoadingStore {
  // 状态存储 - 使用 Map 来存储每个贴纸的状态
  loadingStates: Map<string | number, StickerLoadingState>

  // 状态查询
  getStickerState: (stickerId: string | number) => StickerLoadingState
  
  // 状态更新
  updateStickerState: (stickerId: string | number, updates: Partial<Omit<StickerLoadingState, 'stickerId' | 'lastUpdated'>>) => void
  
  // 批量操作
  clearAllStates: () => void
  clearStickerState: (stickerId: string | number) => void
  
  // 内部方法
  _createDefaultState: (stickerId: string | number) => StickerLoadingState
}

/**
 * 创建默认的贴纸加载状态
 */
const createDefaultState = (stickerId: string | number): StickerLoadingState => ({
  stickerId,
  coverLoaded: false,
  thumbLoaded: false,
  thumbLoading: false,
  fileLoaded: false,
  fileLoading: false,
  currentLayer: 'cover',
  lastUpdated: Date.now()
})

/**
 * 贴纸加载状态管理 Store
 * 使用 Zustand 进行状态管理，支持贴纸的三层加载逻辑
 */
export const useStickerLoadingStore = create<StickerLoadingStore>((set, get) => ({
  loadingStates: new Map<string | number, StickerLoadingState>(),
  _createDefaultState: createDefaultState,

  /**
   * 获取指定贴纸的加载状态
   * 如果不存在则创建默认状态
   */
  getStickerState: (stickerId: string | number): StickerLoadingState => {
    const { loadingStates } = get()
    
    if (!loadingStates.has(stickerId)) {
      const defaultState = createDefaultState(stickerId)
      
      // 创建新状态并更新 store
      set(state => {
        const newLoadingStates = new Map(state.loadingStates)
        newLoadingStates.set(stickerId, defaultState)
        return { loadingStates: newLoadingStates }
      })
      
      return defaultState
    }
    
    return loadingStates.get(stickerId)!
  },

  /**
   * 更新指定贴纸的加载状态
   */
  updateStickerState: (
    stickerId: string | number, 
    updates: Partial<Omit<StickerLoadingState, 'stickerId' | 'lastUpdated'>>
  ) => {
    set(state => {
      const newLoadingStates = new Map(state.loadingStates)
      const currentState = newLoadingStates.get(stickerId) || createDefaultState(stickerId)
      
      const updatedState: StickerLoadingState = {
        ...currentState,
        ...updates,
        stickerId, // 确保 stickerId 不被覆盖
        lastUpdated: Date.now()
      }
      
      newLoadingStates.set(stickerId, updatedState)
      
      console.log(`[StickerLoadingStore] 更新贴纸状态:`, {
        stickerId,
        updates,
        newState: updatedState
      })
      
      return { loadingStates: newLoadingStates }
    })
  },

  /**
   * 清除指定贴纸的状态
   */
  clearStickerState: (stickerId: string | number) => {
    set(state => {
      const newLoadingStates = new Map(state.loadingStates)
      newLoadingStates.delete(stickerId)
      
      console.log(`[StickerLoadingStore] 清除贴纸状态:`, { stickerId })
      
      return { loadingStates: newLoadingStates }
    })
  },

  /**
   * 清除所有贴纸状态
   */
  clearAllStates: () => {
    set(() => {
      console.log(`[StickerLoadingStore] 清除所有贴纸状态`)
      return { loadingStates: new Map() }
    })
  }
}))

/**
 * 选择器：获取指定贴纸的状态
 */
export const selectStickerState = (stickerId: string | number) => 
  (state: StickerLoadingStore) => state.getStickerState(stickerId)

/**
 * 选择器：检查指定贴纸是否正在加载任何层级
 */
export const selectStickerIsLoading = (stickerId: string | number) => 
  (state: StickerLoadingStore) => {
    const stickerState = state.getStickerState(stickerId)
    return stickerState.thumbLoading || stickerState.fileLoading
  }

/**
 * 选择器：获取指定贴纸的当前层级
 */
export const selectStickerCurrentLayer = (stickerId: string | number) => 
  (state: StickerLoadingStore) => state.getStickerState(stickerId).currentLayer
