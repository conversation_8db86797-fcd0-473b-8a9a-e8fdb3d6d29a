import { useStickerLoadingStore, selectStickerState, type LoadingLayer } from './useStickerLoadingStore'

/**
 * 兼容旧 API 的贴纸加载状态接口
 * 保持与 PasterResource.StickerLoadingState 的兼容性
 */
export interface LegacyStickerLoadingState {
  coverId: string | number
  coverLoaded: boolean
  thumbLoaded: boolean
  thumbLoading: boolean
  fileLoaded: boolean
  fileLoading: boolean
  currentLayer: LoadingLayer
}

/**
 * Hook for managing sticker loading state
 * 重构后使用 Zustand store，移除轮询机制，提供响应式状态更新
 */
export const useStickerLoadingState = (stickerId: string | number): LegacyStickerLoadingState => {
  // 直接使用 Zustand store 的状态，自动响应状态变化
  const stickerState = useStickerLoadingStore(selectStickerState(stickerId))

  // 转换为兼容旧 API 的格式
  return {
    coverId: stickerState.stickerId,
    coverLoaded: stickerState.coverLoaded,
    thumbLoaded: stickerState.thumbLoaded,
    thumbLoading: stickerState.thumbLoading,
    fileLoaded: stickerState.fileLoaded,
    fileLoading: stickerState.fileLoading,
    currentLayer: stickerState.currentLayer
  }
}
