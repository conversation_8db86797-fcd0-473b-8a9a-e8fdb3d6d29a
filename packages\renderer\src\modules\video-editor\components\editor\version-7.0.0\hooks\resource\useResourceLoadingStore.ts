import { create } from 'zustand'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'

export interface ResourceLoadingState {
  url: string
  type: ResourceType
  isLoading: boolean
  timestamp: number

}

export interface ResourceLoadingStore {
  loadingStates: Map<string, ResourceLoadingState>

  isResourceLoading: (url: string, type: ResourceType) => boolean
  startResourceLoading: (url: string, type: ResourceType) => boolean
  endResourceLoading: (url: string, type: ResourceType) => boolean

  _generateKey: (url: string, type: ResourceType) => string
}

const generateKey = (url: string, type: ResourceType): string => {
  return `${type}:${url}`
}

export const useResourceLoadingStore = create<ResourceLoadingStore>((set, get) => ({
  loadingStates: new Map<string, ResourceLoadingState>(),
  _generateKey: generateKey,

  /**
   * 检查指定资源是否正在加载中
   * @param url 资源URL
   * @param type 资源类型
   * @returns 是否正在加载
   */
  isResourceLoading: (url: string, type: ResourceType): boolean => {
    if (!url || !type) return false
    const { loadingStates } = get()

    const key = generateKey(url, type)
    const state = loadingStates.get(key)
    return state?.isLoading || false
  },

  /**
   * 标记指定资源开始加载
   * @param url 资源URL
   * @param type 资源类型
   * @returns 操作是否成功
   */
  startResourceLoading: (url: string, type: ResourceType): boolean => {
    try {
      // 参数验证
      if (!url || !type) {
        console.warn('[ResourceStore] startResourceLoading: 参数无效', { url, type })
        return false
      }

      if (!Object.values(ResourceType).includes(type)) {
        console.error('[ResourceStore] startResourceLoading: 无效的资源类型', type)
        return false
      }

      const key = generateKey(url, type)

      set(state => {
        const newLoadingStates = new Map(state.loadingStates)
        newLoadingStates.set(key, {
          url,
          type,
          isLoading: true,
          timestamp: Date.now()
        })

        console.log('[ResourceStore] startResourceLoading:', { key, size: newLoadingStates.size })
        return { loadingStates: newLoadingStates }
      })

      return true
    } catch (error) {
      console.error('[ResourceStore] startResourceLoading 失败:', error)
      return false
    }
  },

  /**
   * 标记指定资源加载完成
   * @param url 资源URL
   * @param type 资源类型
   * @returns 操作是否成功
   */
  endResourceLoading: (url: string, type: ResourceType): boolean => {
    try {
      // 参数验证
      if (!url || !type) {
        console.warn('[ResourceStore] endResourceLoading: 参数无效', { url, type })
        return false
      }

      if (!Object.values(ResourceType).includes(type)) {
        console.error('[ResourceStore] endResourceLoading: 无效的资源类型', type)
        return false
      }

      const key = generateKey(url, type)

      set(state => {
        const newLoadingStates = new Map(state.loadingStates)
        const existingState = newLoadingStates.get(key)

        if (existingState) {
          newLoadingStates.set(key, {
            ...existingState,
            isLoading: false,
            timestamp: Date.now()
          })
        }

        console.log('[ResourceStore] endResourceLoading:', { key, existingState, size: newLoadingStates.size })
        return { loadingStates: newLoadingStates }
      })

      return true
    } catch (error) {
      console.error('[ResourceStore] endResourceLoading 失败:', error)
      return false
    }
  }
}))

