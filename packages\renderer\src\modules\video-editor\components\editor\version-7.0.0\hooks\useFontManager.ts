import { useCallback } from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { useResource } from './resource/useResource.tsx'
import * as opentype from 'opentype.js'
import { TextToSvgConvertor } from '../../../renderer/utils/text-to-svg'

// 统一的字体标识符
interface FontIdentifier {
  path: string           // 字体文件路径
  name: string          // 字体名称
}

// 统一的字体缓存项
interface UnifiedFontCacheItem {
  identifier: FontIdentifier
  opentypeFont?: opentype.Font  // opentype.js 字体对象（可选）
  domLoaded: boolean           // 是否已在 DOM 中加载
  loadedAt: number            // 加载时间戳
}

// 统一的字体加载状态
interface UnifiedFontLoadingState {
  identifier: FontIdentifier
  isLoading: boolean
  promise: Promise<boolean | opentype.Font | null> | null
  result: boolean | opentype.Font | null
  error: Error | null
  startedAt: number
}

// 统一的字体缓存管理器
class FontCacheManager {

  private static instance: FontCacheManager | null = null

  // 统一的字体缓存和状态管理
  private fontCache = new Map<string, UnifiedFontCacheItem>()
  private loadingStates = new Map<string, UnifiedFontLoadingState>()
  private downloadQueue = new Map<string, Promise<string | null>>()

  private debounceTimers = new Map<string, NodeJS.Timeout>()

  static getInstance(): FontCacheManager {
    if (!FontCacheManager.instance) {
      FontCacheManager.instance = new FontCacheManager()
    }
    return FontCacheManager.instance
  }

  // ========== 统一字体管理核心方法 ==========

  /**
   * 生成字体的唯一键
   */
  private getFontKey(identifier: FontIdentifier): string {
    return `${identifier.name}:${identifier.path}`
  }

  /**
   * 创建字体标识符
   */
  createFontIdentifier(path: string, name: string): FontIdentifier {
    return { path, name }
  }

  /**
   * 检查字体是否已加载（统一接口）
   */
  isFontLoaded(identifier: FontIdentifier): boolean {
    const key = this.getFontKey(identifier)
    const cached = this.fontCache.get(key)
    return cached?.domLoaded || false
  }

  /**
   * 获取缓存的字体
   */
  getCachedFont(identifier: FontIdentifier): UnifiedFontCacheItem | null {
    const key = this.getFontKey(identifier)
    return this.fontCache.get(key) || null
  }

  /**
   * 统一的字体加载方法
   * @param identifier 字体标识符
   * @param loadFunction 具体的加载函数
   * @param needsOpentypeFont 是否需要 opentype.Font 对象
   */
  async loadFont(
    identifier: FontIdentifier,
    loadFunction: () => Promise<boolean | opentype.Font>,
    needsOpentypeFont: boolean = false
  ): Promise<boolean | opentype.Font | null> {
    const key = this.getFontKey(identifier)

    // 检查是否已缓存
    const cached = this.fontCache.get(key)
    if (cached?.domLoaded) {
      if (needsOpentypeFont && cached.opentypeFont) {
        return cached.opentypeFont
      } else if (!needsOpentypeFont) {
        return true
      }
    }

    // 检查是否正在加载
    const existingState = this.loadingStates.get(key)
    if (existingState?.isLoading && existingState.promise) {
      return await existingState.promise
    }

    // 创建加载 Promise
    const loadPromise = this.executeLoad(identifier, loadFunction)

    // 设置加载状态
    this.loadingStates.set(key, {
      identifier,
      isLoading: true,
      promise: loadPromise,
      result: null,
      error: null,
      startedAt: Date.now()
    })

    return await loadPromise
  }

  /**
   * 执行字体加载
   */
  private async executeLoad(
    identifier: FontIdentifier,
    loadFunction: () => Promise<boolean | opentype.Font>
  ): Promise<boolean | opentype.Font | null> {
    const key = this.getFontKey(identifier)

    try {
      const result = await loadFunction()

      // 缓存成功结果
      const opentypeFont = (typeof result === 'object' && result !== null) ? result as opentype.Font : undefined
      this.fontCache.set(key, {
        identifier,
        opentypeFont,
        domLoaded: true,
        loadedAt: Date.now()
      })

      // 更新状态
      this.loadingStates.set(key, {
        identifier,
        isLoading: false,
        promise: null,
        result,
        error: null,
        startedAt: Date.now()
      })

      return result
    } catch (error) {
      // 更新错误状态
      this.loadingStates.set(key, {
        identifier,
        isLoading: false,
        promise: null,
        result: null,
        error: error as Error,
        startedAt: Date.now()
      })

      throw error
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    // 清除所有防抖定时器
    this.debounceTimers.forEach(timer => clearTimeout(timer))
    this.debounceTimers.clear()

    // 清空下载队列
    this.downloadQueue.clear()

    // 清理已完成的加载状态（保留缓存）
    for (const [key, state] of this.loadingStates.entries()) {
      if (!state.isLoading) {
        this.loadingStates.delete(key)
      }
    }
  }
}

const unifiedFontManager = FontCacheManager.getInstance()

/**
 * 判断是否是本地字体文件（public 目录中的字体）
 * @param src 字体文件路径
 * @returns 是否是本地字体
 */
function isLocalFont(src: string): boolean {
  // 检查是否是以 /fonts/ 或 ./fonts/ 开头的本地字体路径
  return src.startsWith('/fonts/') || src.startsWith('./fonts/')
}

// 统一的字体管理 Hook
export function useFontManager() {
  const { getResourcePathSync, downloadResourceToCache } = useResource()

  // ========== 统一字体管理方法 ==========

  /**
   * 创建字体标识符的便捷方法
   */
  const createFontId = useCallback((path: string, name: string) => {
    return unifiedFontManager.createFontIdentifier(path, name)
  }, [])

  /**
   * 统一的字体加载方法
   */
  const loadUnifiedFont = useCallback(async (
    identifier: FontIdentifier,
    loadFunction: () => Promise<boolean | opentype.Font>,
    needsOpentypeFont: boolean = false
  ) => {
    return await unifiedFontManager.loadFont(identifier, loadFunction, needsOpentypeFont)
  }, [])

  /**
   * 检查字体是否已加载
   */
  const isFontLoaded = useCallback((identifier: FontIdentifier) => {
    return unifiedFontManager.isFontLoaded(identifier)
  }, [])

  /**
   * 加载字体并返回 opentype.Font 对象
   */
  const loadFontWithOpentype = useCallback(async (fontPath: string, fontName: string) => {
    const identifier = createFontId(fontPath, fontName)

    const result = await loadUnifiedFont(identifier, async () => {
      try {
        let fontUrl: string
        let opentypeFont: opentype.Font

        // 检查是否是本地字体文件（public 目录中的字体）
        if (isLocalFont(fontPath)) {
          console.log(`[字体加载] 加载本地字体: ${fontPath}`)
          // 直接通过 HTTP 路径加载本地字体
          fontUrl = fontPath
          opentypeFont = await opentype.load(fontUrl)
        } else {
          // 处理远程字体文件
          // 检查是否已缓存到本地
          let localPath = getResourcePathSync(ResourceType.FONT, fontPath)

          if (!localPath) {
            // 如果未缓存，先下载
            localPath = await downloadResourceToCache({
              url: fontPath,
              resourceType: ResourceType.FONT,
              version: '1.0'
            })
          }

          if (!localPath) {
            throw new Error(`无法获取字体文件: ${fontPath}`)
          }

          // 构建字体 URL
          const buildFontUrl = (path: string) => {
            if (path.startsWith('http://') || path.startsWith('https://')) {
              return path
            }

            const normalizedPath = path.replace(/\\/g, '/')
            if (normalizedPath.startsWith('/')) {
              return `file://${normalizedPath}`
            }

            return `file:///${normalizedPath}`
          }

          fontUrl = buildFontUrl(localPath)
          // 加载 opentype.Font 对象
          opentypeFont = await opentype.load(fontUrl)
        }

        // 同时加载到 DOM
        try {
          const fontFace = new FontFace(fontName, `url("${fontUrl}")`)
          await fontFace.load()
          document.fonts.add(fontFace)
        } catch (domError) {
          console.warn('[字体加载] DOM 字体加载失败，但 opentype.js 加载成功:', domError)
        }

        return opentypeFont
      } catch (error) {
        console.error(`[字体加载] 加载失败: ${fontName}`, error)
        throw error
      }
    }, true) // 需要 opentype.Font 对象

    return typeof result === 'object' && result !== null ? result as opentype.Font : null
  }, [createFontId, loadUnifiedFont, getResourcePathSync, downloadResourceToCache])

  /**
   * 计算文字尺寸的便捷方法
   */
  const calculateTextSize = useCallback(async (
    text: string = '默认文字',
    fontSize: number = 50,
    fontPath: string,
    fontName: string
  ): Promise<{ width: number; height: number } | null> => {
    try {
      // 加载字体并获取 opentype.Font 对象
      const font = await loadFontWithOpentype(fontPath, fontName)
      if (!font) {
        console.warn('[文字尺寸计算] 字体加载失败')
        return null
      }

      // 创建 TextToSvgConvertor 实例
      const convertor = new TextToSvgConvertor(font, font)

      // 计算尺寸
      const width = convertor._getWidth(text, fontSize)
      const height = convertor._getHeight(fontSize)

      return { width, height }
    } catch (error) {
      console.error('[文字尺寸计算] 计算失败:', error)
      return null
    }
  }, [loadFontWithOpentype])

  /**
   * 加载普通字体（向后兼容）
   */
  const loadFont = useCallback(async (fontPath: string, fontName: string) => {
    const identifier = createFontId(fontPath, fontName)

    const result = await loadUnifiedFont(identifier, async () => {
      // 原有的 DOM 字体加载逻辑
      try {
        let fontUrl: string

        // 检查是否是本地字体文件（public 目录中的字体）
        if (isLocalFont(fontPath)) {
          console.log(`[字体加载] 加载本地字体到 DOM: ${fontPath}`)
          // 直接使用 HTTP 路径
          fontUrl = fontPath
        } else {
          // 处理远程字体文件
          // 检查是否已缓存到本地
          let localPath = getResourcePathSync(ResourceType.FONT, fontPath)

          if (!localPath) {
            // 如果未缓存，先下载
            localPath = await downloadResourceToCache({
              url: fontPath,
              resourceType: ResourceType.FONT,
              version: '1.0'
            })
          }

          if (!localPath) {
            throw new Error(`无法获取字体文件: ${fontPath}`)
          }

          fontUrl = localPath
        }

        // 创建 @font-face CSS
        const fontFace = new FontFace(fontName, `url("${fontUrl}")`)
        await fontFace.load()
        ;(document.fonts as any).add(fontFace)

        return true
      } catch (error) {
        console.error(`[字体加载] 加载失败: ${fontName}`, error)
        return false
      }
    })

    return typeof result === 'boolean' ? result : false
  }, [createFontId, loadUnifiedFont, getResourcePathSync, downloadResourceToCache])

  /**
   * 加载花体字字体（向后兼容）
   */
  const loadFontStyleFont = useCallback(async (fontPath: string, fontName: string) => {
    const identifier = createFontId(fontPath, fontName)

    const result = await loadUnifiedFont(identifier, async () => {
      // 复用普通字体的加载逻辑
      return await loadFont(fontPath, fontName)
    })

    return typeof result === 'boolean' ? result : false
  }, [createFontId, loadUnifiedFont, loadFont])

  /**
   * 检查花体字是否已加载（向后兼容）
   */
  const isFontStyleLoaded = useCallback((fontPath: string) => {
    // 创建一个临时标识符进行检查
    const identifier = createFontId(fontPath, fontPath)
    return isFontLoaded(identifier)
  }, [createFontId, isFontLoaded])

  return {
    isFontStyleLoaded,
    loadFontStyleFont,
    calculateTextSize,

    // 清理
    cleanup: useCallback(() => unifiedFontManager.cleanup(), [])
  }
}
