import React, { PropsWith<PERSON>hildren, useCallback, useMemo, useState } from 'react'
import { OverlayType, TrackType, VideoOverlay } from '@app/rve-shared/types'
import { RenderRequestPayload } from '@app/shared/types/ipc/editor.ts'
import { calculateDuration, calculateRenderableOverlays } from '@rve/editor/utils/overlay-helper.ts'
import { queryFirstKeyframeOfVideo } from '@/hooks/queries/useQueryVideoKeyframe.ts'
import { toast } from 'react-toastify'
import { queryClient } from '@/main.tsx'
import { QUERY_KEYS } from '@/constants/queryKeys.ts'
import { EditorState } from '@/libs/cache/parts/editor.cache.ts'
import { useVirtualTab } from '@/contexts'
import { sleep } from '@app/shared/utils.ts'
import { useQuery } from '@tanstack/react-query'
import { EditorModule } from '@/libs/request/api/editor.ts'
import { GeneratedMixcut, MixcutContext, MixcutContextValues, MixcutPageTabs, useMultiSelection } from './context'

const useGenerationPart = (state: EditorState, scriptId: string) => {
  const [limit] = useState(10)
  const [generatedMixcuts, setGeneratedMixcuts] = useState<GeneratedMixcut[]>([])
  const [batchUploadState, setBatchUploadState] = useState<MixcutContextValues['generation']['batchUploadState']>({
    visible: false,
    completed: 0,
    total: 0,
  })

  const multiSelection = useMultiSelection(generatedMixcuts)

  // 批量上传方法
  const uploadSelectedPreviews = useCallback(
    async () => {
      const { selectedIndices, setSelectedIndices } = multiSelection

      const selectedIndicesArray = Array.from(selectedIndices)
      if (selectedIndicesArray.length === 0) {
        toast.warning('请先选择要上传的混剪结果')
        return
      }

      setBatchUploadState({
        visible: true,
        completed: 0,
        total: selectedIndicesArray.length,
      })

      try {
        for (let i = 0; i < selectedIndicesArray.length; i++) {
          const index = selectedIndicesArray[i]
          const combo = generatedMixcuts[index]

          setBatchUploadState(prev => ({
            ...prev,
            currentItem: `组合 [${combo.combination.join(', ')}]`
          }))

          // 获取第一个视频overlay用于封面
          const { tracks, playerMetadata } = state
          const firstStoryboardTrackIndex = combo.combination[0]
          const targetVideoTrack = tracks[firstStoryboardTrackIndex]
          const firstVideoOverlay = targetVideoTrack?.overlays.find(
            (overlay: any) => overlay.storyboardIndex === 0 && overlay.type === OverlayType.VIDEO
          ) as VideoOverlay | null

          const overlays = calculateRenderableOverlays(tracks, {
            [TrackType.VIDEO]: combo.combination
          })

          const data: RenderRequestPayload = {
            id: 'V0-0-0',
            inputProps: {
              overlays,
              playerMetadata
            }
          }

          // 调用上传方法
          await Promise.all([
            window.editor.uploadMixcutResult({
              scriptId,
              data,
              similarity: combo.similarity,
              cover: await queryFirstKeyframeOfVideo(firstVideoOverlay?.src) || undefined,
              duration: calculateDuration(overlays)
            }),
            new Promise(resolve => setTimeout(resolve, 1500))
          ])

          setBatchUploadState(prev => ({
            ...prev,
            completed: i + 1
          }))
        }

        toast.success(`成功保存 ${selectedIndicesArray.length} 个混剪结果！`)

        setGeneratedMixcuts(prev => {
          return prev.filter((_, index) => !selectedIndicesArray.includes(index))
        })
        setSelectedIndices(new Set())

        void queryClient.refetchQueries({ queryKey: [QUERY_KEYS.SAVED_MIXCUT_LIST] })
        await sleep(1000)
      } catch (error) {
        console.error('批量上传失败:', error)
        toast.error(`批量上传失败: ${error instanceof Error ? error.message : '未知错误'}`)
      } finally {
        setBatchUploadState({
          visible: false,
          completed: 0,
          total: 0,
        })
      }
    },
    [multiSelection, generatedMixcuts, state]
  )

  const generateCombinations = useCallback(async () => {
    const { tracks } = state

    const storyboardTrack = tracks.find(track => track.type === TrackType.STORYBOARD)
    if (!storyboardTrack) {
      return []
    }

    const storyboards = storyboardTrack.overlays
    const videoTracks = tracks
      .filter(t => t.type === TrackType.VIDEO && !t.isGlobalTrack)
      .map((track, index) => ({ ...track, index: index + 1 }))

    const matrix = storyboards
      .map((_, storyboardIndex) => {
        return videoTracks
          .filter(t => t.overlays.some(o => o.storyboardIndex === storyboardIndex))
          .map(o => o.index)
      })

    setGeneratedMixcuts(
      await window.editor.generateCombos({
        limit,
        threshold: 1,
        matrix,
      })
    )
    // 清空选择状态
    multiSelection.setSelectedIndices(new Set())
  }, [state.tracks, limit])

  return {
    generatedMixcuts,
    batchUploadState,
    uploadSelectedPreviews,
    generateCombinations,

    ...multiSelection,
  }
}

const useSavedPart = (_state: EditorState, scriptId: string) => {
  const { data } = useQuery({
    queryKey: [QUERY_KEYS.SAVED_MIXCUT_LIST, scriptId],
    queryFn: () => EditorModule.listMixcuts(scriptId)
  })

  const multiSelection = useMultiSelection(data?.list || [])

  return {
    ...multiSelection,
  }
}

export const MixcutProvider: React.FC<PropsWithChildren<{ state: EditorState }>> = ({
  children, state
}) => {
  const { params } = useVirtualTab('Mixcut')
  const scriptId = params?.id

  const [activeTab, setActiveTab] = useState<MixcutPageTabs>('generation')

  const generationPart = useGenerationPart(state, scriptId)
  const savedPart = useSavedPart(state, scriptId)

  const playerOverlays = useMemo(() => {
    if (activeTab === 'generation' && generationPart.activeItem) {
      return calculateRenderableOverlays(state.tracks, {
        [TrackType.VIDEO]: generationPart.activeItem.combination
      })
    }

    return []
  }, [activeTab, state.tracks, generationPart.activeItem, savedPart.activeItem])

  return (
    <MixcutContext.Provider
      value={{
        state,

        generation: generationPart,
        saved: savedPart,

        playerOverlays,
        activeTab,
        setActiveTab,
        rules: {
          material: {
            enableShuffle: false,
            enableSmartTrim: false
          },
          deduplicate: {
            enableDeduplicate: false,
            operationStatus: new Map()
          }
        },
      }}
    >
      {children}
    </MixcutContext.Provider>
  )
}
