import React, { memo, useCallback, useMemo, useState } from 'react'
import { SoundResource } from '@/types/resources.ts'
import { ResourcePanelLayout } from '../overlays/common/resource-panel-layout.tsx'
import { Music } from 'lucide-react'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { InfiniteResourceList } from '@/components/InfiniteResourceList.tsx'
import { MusicResourceTabs, ResourceTabType } from '../../templates/sticker-templates/constants.ts'
import { AudioResourceItem } from '../overlays/common/audio-resource-item.tsx'
import {
  useInfiniteQueryMusicRankList,
  useInfiniteQueryMusicUnified,
  useQueryMusicCategory
} from '@/hooks/queries/useQueryMusic.ts'
import { filterByDuration } from '../duration-filter'

export function MusicPanel() {
  const { data: musicCategory } = useQueryMusicCategory()

  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [searchKey, setSearchKey] = useState<string>('')
  const [activeTab, setActiveTab] = useState<string>(ResourceTabType.ONLINE)
  const [selectedDuration, setSelectedDuration] = useState<string>('')

  const infiniteQueryResult = useInfiniteQueryMusicUnified({
    pageSize: 50,
    selectedCategory: selectedCategory,
    search: searchKey,
    enabled: activeTab === ResourceTabType.ONLINE
  })

  const infiniteRankQueryResult = useInfiniteQueryMusicRankList({
    pageSize: 50,
    search: searchKey,
    enabled: activeTab === ResourceTabType.RANK
  })

  const renderMusicItem = useCallback((item: SoundResource.Sound, index: number) => {
    return (
      <div className="relative">
        <AudioResourceItem
          key={index}
          item={item}
          icon={<Music className="w-8 h-8" />}
          resourceType={ResourceType.MUSIC}
          customExt="mp3"
          showCollectionButton={true}
        />
      </div>
    )
  }, [AudioResourceItem])

  const renderMusicContent = useCallback(() => {
    const filteredQueryResult = useMemo(() => {
      if (!infiniteQueryResult.data || !selectedDuration) {
        return infiniteQueryResult
      }

      return {
        ...infiniteQueryResult,
        data: {
          ...infiniteQueryResult.data,
          pages: infiniteQueryResult.data.pages.map(page => ({
            ...page,
            list: filterByDuration(page.list, selectedDuration)
          }))
        }
      }
    }, [infiniteQueryResult, selectedDuration])

    return (
      <InfiniteResourceList
        queryResult={filteredQueryResult}
        renderItem={renderMusicItem}
        emptyText="该分类暂无音效"
        loadingText="加载音效中..."
        itemsContainerClassName="grid grid-cols-3 gap-3 pt-3 pb-3"
      />
    )
  }, [infiniteQueryResult, renderMusicItem, selectedDuration])

  const renderMusicRankContent = useCallback(() => {
    const filteredRankQueryResult = useMemo(() => {
      if (!infiniteRankQueryResult.data || !selectedDuration) {
        return infiniteRankQueryResult
      }

      return {
        ...infiniteRankQueryResult,
        data: {
          ...infiniteRankQueryResult.data,
          pages: infiniteRankQueryResult.data.pages.map(page => ({
            ...page,
            list: filterByDuration(page.list, selectedDuration)
          }))
        }
      }
    }, [infiniteRankQueryResult, selectedDuration])

    return (
      <InfiniteResourceList
        queryResult={filteredRankQueryResult}
        renderItem={renderMusicItem}
        emptyText="该分类暂无音效"
        loadingText="加载音效中..."
        itemsContainerClassName="grid grid-cols-3 gap-3 pt-3 pb-3"      
      />
    )
  }, [infiniteRankQueryResult, renderMusicItem, selectedDuration])

  const renderLocalStickerContent = useCallback(() => {
    // const exampleResources: LocalResourceItem[] = Array(8).fill(0).map((_, index) => ({
    //   id: `music-${index}`,
    //   type: 'music',
    //   name: `音乐 ${index + 1}`,
    //   path: ''
    // }))

    return (
      <div>
        1
      </div>
      // <LocalResourcePanel
      //   resources={exampleResources}
      //   emptyText="暂无本地音乐"
      //   renderResourceItem={(_resource, index) => (
      //     <div key={index} className="aspect-square bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
      //       <Music className="w-8 h-8 text-gray-400" />
      //     </div>
      //   )}
      // />
    )
  }, [])

  const hasMusic = useMemo(() => {
    const firstPage = infiniteQueryResult.data?.pages?.[0]
    if (!firstPage) return false

    // 如果有时长筛选，检查筛选后的结果
    if (selectedDuration) {
      const filteredList = filterByDuration(firstPage.list, selectedDuration)
      return filteredList.length > 0
    }

    return firstPage.list.length > 0
  }, [infiniteQueryResult.data, selectedDuration])

  const hasRankMusic = useMemo(() => {
    const firstPage = infiniteRankQueryResult.data?.pages?.[0]
    if (!firstPage) return false

    if (selectedDuration) {
      const filteredList = filterByDuration(firstPage.list, selectedDuration)
      return filteredList.length > 0
    }

    return firstPage.list.length > 0
  }, [infiniteRankQueryResult.data, selectedDuration])

  const tabContentRenderers = useMemo(() => ({
    [ResourceTabType.ONLINE]: renderMusicContent,
    [ResourceTabType.RANK]: renderMusicRankContent,
    [ResourceTabType.LOCAL]: renderLocalStickerContent,
  }), [renderMusicContent, renderMusicRankContent, renderLocalStickerContent])

  // 空状态配置映射
  const emptyStateConfig = useMemo(() => ({
    [ResourceTabType.ONLINE]: {
      isEmpty: !hasMusic,
      emptyText: '该分类暂无音乐'
    },
    [ResourceTabType.RANK]: {
      isEmpty: !hasRankMusic,
      emptyText: '暂无热门音乐'
    },
    [ResourceTabType.LOCAL]: {
      isEmpty: false, // 本地资源暂时不检查空状态
      emptyText: '暂无本地音乐'
    },
  }), [hasMusic, hasRankMusic])

  // 配置标签页内容
  const tabsWithContent = useMemo(() => {
    return MusicResourceTabs.map(tab => {
      const renderer = tabContentRenderers[tab.value as keyof typeof tabContentRenderers]
      const emptyState = emptyStateConfig[tab.value as keyof typeof emptyStateConfig]

      return {
        ...tab,
        renderContent: renderer || (() => null),
        isEmpty: emptyState?.isEmpty || false,
        emptyText: emptyState?.emptyText || '暂无数据',
        showDurationFilter: tab.value === ResourceTabType.ONLINE 
      }
    })
  }, [tabContentRenderers, emptyStateConfig])

  return (
    <ResourcePanelLayout
      tabs={tabsWithContent}
      defaultTab={ResourceTabType.ONLINE}
      categories={musicCategory}
      selectedCategory={selectedCategory}
      onCategoryChange={ setSelectedCategory}
      searchKey={searchKey}
      onSearchChange={setSearchKey}
      onTabChange={setActiveTab}
      selectedDuration={selectedDuration}
      onDurationChange={setSelectedDuration}
    />
  )
}

export default memo(MusicPanel)
