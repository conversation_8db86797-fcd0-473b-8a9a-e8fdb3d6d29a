import React from 'react'
import { OverlayType } from '@app/rve-shared/types'
import { registerMaterialPlugin } from '../registry'
import { Type } from 'lucide-react'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'

const Panel = React.lazy(() =>
  import('@rve/editor/components/plugin-panels/text.panel')
)

export default registerMaterialPlugin({
  id: ResourceType.FONT,
  title: '文字',
  icon: Type,
  component: Panel,
  overlayType: OverlayType.TEXT,
  order: 90,
})

